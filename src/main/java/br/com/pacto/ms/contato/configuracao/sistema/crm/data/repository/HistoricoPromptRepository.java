package br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.HistoricoPromptEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;

public interface HistoricoPromptRepository extends JpaRepository<HistoricoPromptEntity, Integer> {

    @Query("select h from HistoricoPromptEntity h " +
            "where h.codigoempresa = :codigoEmpresa " +
            "and h.configuracao.codigo = :codigoConfiguracao " +
            "order by h.criadoEm desc ")
    List<HistoricoPromptEntity> obterHistoricoPorEmpresa(Integer codigoEmpresa, Integer codigoConfiguracao);



    @Modifying
    @Query("delete from HistoricoPromptEntity h where h.criadoEm < :dataLimite")
    int deleteBycriadoEmBefore(@Param("dataLimite") LocalDateTime dataLimite);

}
