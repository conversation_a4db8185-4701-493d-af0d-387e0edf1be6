package br.com.pacto.ms.contato.ia.worker;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository.HistoricoPromptRepository;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.HistoricoPromptService;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.HistoricoScheduleService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;



@Component
public class HistoricoScheduler {

    @Autowired
    private HistoricoScheduleService historicoScheduleService;

    @Value("${app.historico-prompt.retention-days:30}")
    private int retentionDays;

    private static final Logger log = LoggerFactory.getLogger(HistoricoScheduler.class);

    @Scheduled(cron = "0 */1 * * * *")
    public void executarLimpezaAutomatica() {
        log.info("Iniciando limpeza automática do histórico de prompts");

        try {
            LocalDateTime dataLimite = LocalDateTime.now().minusDays(retentionDays);
            int registrosRemovidos = historicoScheduleService.limparHistoricoAntigo(dataLimite);

            log.info("Removidos {} registros de histórico de prompts anteriores a {} dias",
                    registrosRemovidos, retentionDays);

            if (registrosRemovidos > 0) {
                log.info("Limpeza automática concluída. {} registros removidos do histórico de prompts",
                        registrosRemovidos);
            } else {
                log.debug("Limpeza automática concluída. Nenhum registro antigo encontrado para remoção");
            }
        } catch (Exception e) {
            log.error("Erro durante a limpeza automática do histórico de prompts", e);
        }
    }
}
