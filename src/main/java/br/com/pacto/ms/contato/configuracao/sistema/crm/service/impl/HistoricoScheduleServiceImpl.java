package br.com.pacto.ms.contato.configuracao.sistema.crm.service.impl;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.HistoricoPromptEntity;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository.HistoricoPromptRepository;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.HistoricoScheduleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class HistoricoScheduleServiceImpl implements HistoricoScheduleService {

    @Autowired
    private HistoricoPromptRepository repository;

    @Transactional
    @Override
    public int limparHistoricoAntigo(LocalDateTime dataLimite) {
        List<HistoricoPromptEntity> bycriadoEmBefore = repository.findBycriadoEmBefore(dataLimite);
        int quantidadeItens = bycriadoEmBefore.size();
        repository.deleteAll(bycriadoEmBefore);
        return quantidadeItens;
    }
}
