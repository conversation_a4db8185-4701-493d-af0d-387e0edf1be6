package br.com.pacto.ms.contato.configuracao.sistema.crm.service.impl;

import br.com.pacto.ms.contato.avulso.data.domain.PessoaEntity;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaoCrmIAEntity;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.HistoricoPrompt;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.HistoricoPromptEntity;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.ConfiguracaoCrmIADTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.HistoricoPromptDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.HistoricoPromptOrigemDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository.ConfiguracaoCrmIARepository;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository.HistoricoPromptRepository;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.HistoricoPromptService;
import br.com.pacto.ms.contato.log.service.contract.UsuarioService;
import br.com.pactosolucoes.commons.exception.DataNotMatchException;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.List;


@Slf4j
@Service
@RequiredArgsConstructor
public class HistoricoPromptServiceImpl implements HistoricoPromptService {

    private final HistoricoPromptRepository repository;
    private final ConfiguracaoCrmIARepository configuracaoCrmIARepository;
    private final RequestService requestService;
    private final UsuarioService usuarioService;

    @Override
    public List<HistoricoPromptDTO> consultar(Integer codigoEmpresa, Integer codigoConfiguracao) {
        List<HistoricoPromptEntity> historicoPromptEntities =
                repository.obterHistoricoPorEmpresa(codigoEmpresa, codigoConfiguracao);
        return HistoricoPromptDTO.toDto(historicoPromptEntities);
    }

    public void salvarHistoricoPromptsConfiguracao(
            ConfiguracaoCrmIAEntity configuracaoCrmIAEntity, ConfiguracaoCrmIAEntity configuracaoAnterior
    ) {
        try {
            Class<?> classe = ConfiguracaoCrmIAEntity.class;
            Field[] campos = classe.getDeclaredFields();
            for (Field campo : campos) {
                if (campo.isAnnotationPresent(HistoricoPrompt.class)) {
                    campo.setAccessible(true);
                    Object valorCampo = campo.get(configuracaoCrmIAEntity);
                    String conteudo = (valorCampo != null) ? valorCampo.toString() : null;

                    if (deveSerSalvoNoHistorico(configuracaoAnterior, campo, conteudo)) {
                        Object valorCampoAnterior = campo.get(configuracaoAnterior);
                        String conteudoAnterior = (valorCampoAnterior != null) ? valorCampoAnterior.toString() : null;
                        salvar(configuracaoAnterior, campo.getName(), conteudoAnterior, false);
                    }
                }
            }
        } catch (IllegalAccessException e) {
            throw new RuntimeException("Erro ao acessar os campos da classe ConfiguracaoCrmIAEntity", e);
        } catch (Exception e) {
            throw new RuntimeException("Erro ao salvar histórico de prompts", e);
        }
    }


    public void salvar(ConfiguracaoCrmIAEntity configuracaoCrmIAEntity, String origemCampo, String conteudo, boolean flagRestore) {
        PessoaEntity pessoa = usuarioService.consultarPessoa(requestService.getCurrentConfiguration().getZwId());

        HistoricoPromptEntity entity = HistoricoPromptEntity.builder()
                .configuracao(configuracaoCrmIAEntity)
                .codigoempresa(configuracaoCrmIAEntity.getCodigoEmpresa())
                .origemCampo(origemCampo)
                .restore(flagRestore)
                .conteudo(conteudo)
                .usuario(pessoa.getNome())
                .criadoEm(LocalDateTime.now())
                .build();
        repository.save(entity);
    }

    @Transactional
    public ConfiguracaoCrmIADTO restaurarOrigemHistorico(HistoricoPromptOrigemDTO historicoPromptOrigemDTO) {
        ConfiguracaoCrmIAEntity configuracaoCrmIAEntity = configuracaoCrmIARepository
                .findByCodigoEmpresaAndCodigo(historicoPromptOrigemDTO.getCodigoEmpresa(),
                        historicoPromptOrigemDTO.getCodigoConfiguracao())
                .orElseThrow(() -> {
                    log.error("Configuração não encontrada para empresa: {} e código: {}",
                            historicoPromptOrigemDTO.getCodigoEmpresa(),
                            historicoPromptOrigemDTO.getCodigoConfiguracao());
                    return new DataNotMatchException("Configuração CRM IA não encontrada");
                });

        try {
            salvarValorAtualNoHistorico(configuracaoCrmIAEntity, historicoPromptOrigemDTO.getOrigemCampo(), true);
            restaurarCampoConfiguracao(configuracaoCrmIAEntity, historicoPromptOrigemDTO);
            ConfiguracaoCrmIAEntity entity = configuracaoCrmIARepository.save(configuracaoCrmIAEntity);

            log.info("Histórico de origem restaurado com sucesso para campo: {}. Valor anterior salvo no histórico.",
                    historicoPromptOrigemDTO.getOrigemCampo());

            return ConfiguracaoCrmIADTO.toDto(entity);
        } catch (Exception e) {
            throw new RuntimeException("Erro ao restaurar histórico de origem: " + e.getMessage(), e);
        }
    }

    private void salvarValorAtualNoHistorico(ConfiguracaoCrmIAEntity entity, String nomeCampo, boolean restore)
            throws NoSuchFieldException, IllegalAccessException {
        Field campo = entity.getClass().getDeclaredField(nomeCampo);
        campo.setAccessible(true);
        Object valorAtual = campo.get(entity);
        String conteudoAtual = (valorAtual != null) ? valorAtual.toString() : null;
        salvar(entity, nomeCampo, conteudoAtual, restore);

        log.debug("Valor atual do campo '{}' salvo no histórico: {}", nomeCampo, conteudoAtual);
    }

    private void restaurarCampoConfiguracao(ConfiguracaoCrmIAEntity entity, HistoricoPromptOrigemDTO dto)
            throws NoSuchFieldException, IllegalAccessException {
        Field campo = entity.getClass().getDeclaredField(dto.getOrigemCampo());
        campo.setAccessible(true);
        Object valorConvertido = converterValorParaTipoCampo(campo, dto.getConteudo());
        campo.set(entity, valorConvertido);
        log.debug("Campo '{}' restaurado com valor: {}", dto.getOrigemCampo(), valorConvertido);
    }

    private Object converterValorParaTipoCampo(Field campo, String conteudo) {
        Class<?> tipoCampo = campo.getType();
        if (conteudo == null) {
            return null;
        }
        if (tipoCampo == String.class) {
            return conteudo;
        }
        log.debug("Convertendo valor '{}' para tipo: {}", conteudo, tipoCampo.getSimpleName());
        return conteudo;
    }


    private boolean deveSerSalvoNoHistorico(ConfiguracaoCrmIAEntity configuracaoAnterior, Field campo, String conteudoAtual) {
        if (configuracaoAnterior == null) {
            return true;
        }
        try {
            campo.setAccessible(true);
            Object valorAnterior = campo.get(configuracaoAnterior);
            String conteudoAnterior = (valorAnterior != null) ? valorAnterior.toString() : null;

            return !java.util.Objects.equals(conteudoAtual, conteudoAnterior);
        } catch (IllegalAccessException e) {
            log.warn("Erro ao acessar campo '{}' da configuração anterior. Salvando no histórico por segurança.", campo.getName(), e);
            return true;
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public int limparHistoricoAntigo(LocalDateTime dataLimite) {
        return repository.deleteBycriadoEmBefore(dataLimite);
    }

}
